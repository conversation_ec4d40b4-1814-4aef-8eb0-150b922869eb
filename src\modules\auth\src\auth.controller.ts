import { <PERSON>, <PERSON>, Req, Re<PERSON>, UnauthorizedEx<PERSON>, <PERSON>gger, HttpStatus, Param, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiUnauthorizedResponse, ApiCookieAuth, ApiParam } from '@nestjs/swagger';
import { FastifyRequest, FastifyReply } from 'fastify';
import { AuthSharedService } from './auth.service';

@ApiTags('Authentication')
@Controller('auth')
export class AuthSharedController {
  private readonly logger = new Logger(AuthSharedController.name);

  constructor(private readonly authSharedService: AuthSharedService) {}

  @Get('me')
  @ApiCookieAuth('access_token')
  @ApiOperation({
    summary: 'Get current user information',
    description: 'Authenticates user using encrypted cookies, decrypts access token, verifies JWT locally, and returns user information from JWT payload (no external API calls)'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'JWT payload extracted successfully - returns only actual fields from the JWT token',
    schema: {
      type: 'object',
      properties: {
        iss: { type: 'string', description: 'JWT issuer', example: 'http://srv-captain--ng-auth-dev:3000' },
        sub: { type: 'string', description: 'User ID (subject)', example: 'f7b98e6f-95af-4d54-9c53-312ada49ba6e' },
        aud: { type: 'string', description: 'JWT audience', example: 'ngnair' },
        exp: { type: 'number', description: 'Expiration time (Unix timestamp)', example: 1756774160 },
        iat: { type: 'number', description: 'Issued at time (Unix timestamp)', example: 1756687760 },
        jti: { type: 'string', description: 'JWT ID (UUID)', example: '1af073f4-71e4-4aca-99b9-f41f92770a0a' },
        sid: { type: 'string', description: 'Session ID', example: 'sess_35ee063223007077' },
        azp: { type: 'string', description: 'Authorized party', example: 'webapp' },
        ent_set: { type: 'string', description: 'Entity set (hex string)', example: 'de2e18a49c8b' },
        perm_v: { type: 'number', description: 'Permission version', example: 0 },
        amr: { type: 'array', items: { type: 'string' }, description: 'Authentication methods', example: ['pwd'] },
        auth_time: { type: 'number', description: 'Authentication time (Unix timestamp)', example: 1756687760 },
        email: { type: 'string', description: 'User email (optional - may not be present in all JWTs)', example: '<EMAIL>' }
      },
      required: ['iss', 'sub', 'aud', 'exp', 'iat', 'jti', 'sid', 'azp', 'ent_set', 'perm_v', 'amr', 'auth_time']
    }
  })
  @ApiUnauthorizedResponse({
    description: 'Authentication failed - invalid or missing access token',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Authentication failed' },
        error: { type: 'string', example: 'Unauthorized' }
      }
    }
  })
  async getCurrentUser(
    @Req() request: FastifyRequest,
    @Res() reply: FastifyReply
  ) {
    try {
      // Extract cookies from request
      const cookies = (request as any).cookies || {};

      this.logger.log(`🔐 [AUTH CONTROLLER] /auth/me endpoint called`);
      this.logger.log(`🍪 [AUTH CONTROLLER] Received cookies: ${JSON.stringify(Object.keys(cookies))}`);
      this.logger.log(`🔑 [AUTH CONTROLLER] Access token present: ${!!cookies.access_token}`);

      // Authenticate user using the new method that properly handles external auth service
      const jwtPayload = await this.authSharedService.authenticateUserFromCookies(cookies);

      this.logger.log(`✅ [AUTH CONTROLLER] JWT payload extracted successfully for user: ${jwtPayload.sub}`);
      this.logger.log(`📋 [AUTH CONTROLLER] JWT fields returned: ${JSON.stringify(Object.keys(jwtPayload))}`);

      return reply.status(HttpStatus.OK).send(jwtPayload);
    } catch (error) {
      this.logger.error(`❌ [AUTH CONTROLLER] Authentication failed: ${error.message}`);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  @Get('users/:id')
  @ApiCookieAuth('access_token')
  @ApiOperation({
    summary: 'Get user by ID',
    description: 'Retrieve user information by ID from external auth service using encrypted access token'
  })
  @ApiParam({
    name: 'id',
    description: 'User unique identifier (UUID)',
    type: 'string',
    format: 'uuid'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        email: { type: 'string' },
        first_name: { type: 'string' },
        last_name: { type: 'string' }
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: 'Authentication failed or user not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Failed to retrieve user' },
        error: { type: 'string', example: 'Unauthorized' }
      }
    }
  })
  async getUserById(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() request: FastifyRequest,
    @Res() reply: FastifyReply
  ) {
    try {
      // Extract cookies from request
      const cookies = (request as any).cookies || {};

      this.logger.log(`Getting user by ID: ${id}`);

      // Get user from external auth service
      const user = await this.authSharedService.getUserById(id, cookies);

      return reply.status(HttpStatus.OK).send(user);
    } catch (error) {
      this.logger.error('Get user by ID failed:', error);
      throw new UnauthorizedException(error.message || 'Failed to retrieve user');
    }
  }

  @Get('users')
  @ApiCookieAuth('access_token')
  @ApiOperation({
    summary: 'Get all users',
    description: 'Retrieve all users from external auth service using encrypted access token'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Users retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          email: { type: 'string' },
          first_name: { type: 'string' },
          last_name: { type: 'string' }
        }
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: 'Authentication failed',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Failed to retrieve users' },
        error: { type: 'string', example: 'Unauthorized' }
      }
    }
  })
  async getAllUsers(
    @Req() request: FastifyRequest,
    @Res() reply: FastifyReply
  ) {
    try {
      // Extract cookies from request
      const cookies = (request as any).cookies || {};

      this.logger.log('Getting all users');

      // Get users from external auth service
      const users = await this.authSharedService.getAllUsers(cookies);

      return reply.status(HttpStatus.OK).send(users);
    } catch (error) {
      this.logger.error('Get all users failed:', error);
      throw new UnauthorizedException(error.message || 'Failed to retrieve users');
    }
  }
}
