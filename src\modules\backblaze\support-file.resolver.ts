import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql';
import { UseGuards, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { FileService } from './file.service';
import { SupportFile } from './support-file.model';
import { GraphQLAuthGuard } from '../auth/src/guards';
import { ScopeGuard, SupportAccess, RequirePermissions } from '../auth/src/guards/scope.guard';
import { GraphQLUpload, FileUpload } from 'graphql-upload-minimal';
import { ScopeContext } from '../auth/src/types/scope.types';
import { FileUploadInput, MultipleFileUploadInput } from './dto/file-upload-graphql.dto';

@Resolver(() => SupportFile)
@UseGuards(GraphQLAuthGuard, ScopeGuard)
export class SupportFileResolver {
  private readonly logger = new Logger(SupportFileResolver.name);

  constructor(private readonly fileService: FileService) {}

  @Query(() => SupportFile, { name: 'supportFile' })
  @SupportAccess()
  async supportFile(
    @Args('id', { type: () => String }) id: string,
    @Context() context: any
  ) {
    const scopeContext: ScopeContext = context.req.scopeContext;
    return this.fileService.getFileByIdWithScope(id, scopeContext);
  }

  @Query(() => [SupportFile], { name: 'supportFiles' })
  @SupportAccess()
  async supportFiles(
    @Args('ticketId', { type: () => String, nullable: true }) ticketId?: string,
    @Args('commentId', { type: () => String, nullable: true }) commentId?: string,
    @Context() context?: any
  ) {
    const scopeContext: ScopeContext = context.req.scopeContext;

    // Validate that at least one ID is provided
    if (!ticketId && !commentId) {
      throw new BadRequestException('Either ticketId or commentId must be provided');
    }

    // Validate that both IDs are not provided
    if (ticketId && commentId) {
      throw new BadRequestException('Cannot provide both ticketId and commentId');
    }

    return this.fileService.getFilesWithScope(scopeContext, ticketId, commentId);
  }

  @Mutation(() => SupportFile)
  @RequirePermissions('canUploadFiles')
  async uploadSupportFile(
    @Args({ name: 'file', type: () => GraphQLUpload }) file: FileUpload,
    @Args('ticketId', { type: () => String, nullable: true }) ticketId?: string,
    @Args('commentId', { type: () => String, nullable: true }) commentId?: string,
    @Context() context?: any
  ) {
    const scopeContext: ScopeContext = context.req.scopeContext;
    this.logger.log(`📁 [FILE RESOLVER] Upload request - ticketId: ${ticketId}, commentId: ${commentId}`);

    // Validate file association
    this.validateFileAssociation(ticketId, commentId);

    try {
      // Convert FileUpload to FastifyFileUpload format
      const fastifyFile = await this.convertFileUploadToFastifyFile(file);

      return await this.fileService.uploadFileWithScope(
        fastifyFile,
        scopeContext,
        ticketId,
        commentId
      );
    } catch (error) {
      this.logger.error(`❌ [FILE RESOLVER] Upload failed:`, error.message);
      throw error;
    }
  }

  @Mutation(() => [SupportFile])
  @RequirePermissions('canUploadFiles')
  async uploadMultipleSupportFiles(
    @Args({ name: 'files', type: () => [GraphQLUpload] }) files: FileUpload[],
    @Args('ticketId', { type: () => String, nullable: true }) ticketId?: string,
    @Args('commentId', { type: () => String, nullable: true }) commentId?: string,
    @Context() context?: any
  ) {
    const scopeContext: ScopeContext = context.req.scopeContext;
    this.logger.log(`📁 [FILE RESOLVER] Multiple upload request - ${files.length} files, ticketId: ${ticketId}, commentId: ${commentId}`);

    // Validate file association
    this.validateFileAssociation(ticketId, commentId);

    try {
      const uploadPromises = files.map(async (file) => {
        const fastifyFile = await this.convertFileUploadToFastifyFile(file);
        return this.fileService.uploadFileWithScope(
          fastifyFile,
          scopeContext,
          ticketId,
          commentId
        );
      });

      return await Promise.all(uploadPromises);
    } catch (error) {
      this.logger.error(`❌ [FILE RESOLVER] Multiple upload failed:`, error.message);
      throw error;
    }
  }

  @Mutation(() => SupportFile)
  @RequirePermissions('canDeleteFiles')
  async deleteSupportFile(
    @Args('id', { type: () => String }) id: string,
    @Context() context: any
  ) {
    const scopeContext: ScopeContext = context.req.scopeContext;
    return this.fileService.deleteFileWithScope(id, scopeContext);
  }

  /**
   * Helper method to validate file association
   */
  private validateFileAssociation(ticketId?: string, commentId?: string): void {
    if (!ticketId && !commentId) {
      throw new BadRequestException('Either ticketId or commentId must be provided');
    }

    if (ticketId && commentId) {
      throw new BadRequestException('Cannot provide both ticketId and commentId');
    }
  }

  /**
   * Helper method to convert GraphQL FileUpload to FastifyFileUpload format
   */
  private async convertFileUploadToFastifyFile(file: FileUpload): Promise<any> {
    const stream = file.createReadStream();
    const chunks: Buffer[] = [];

    return new Promise((resolve, reject) => {
      stream.on('data', (chunk: Buffer) => chunks.push(chunk));
      stream.on('end', () => {
        try {
          const fileBuffer = Buffer.concat(chunks);
          const fastifyFile = {
            fieldname: 'file',
            originalname: file.filename,
            encoding: file.encoding || 'utf-8',
            mimetype: file.mimetype,
            buffer: fileBuffer,
            size: fileBuffer.length,
          };
          resolve(fastifyFile);
        } catch (error) {
          reject(error);
        }
      });
      stream.on('error', reject);
    });
  }
}
