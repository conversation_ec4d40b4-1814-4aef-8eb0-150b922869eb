import { Resolver, Query, Mutation, Args, ID, Context } from '@nestjs/graphql';
import { UseGuards, NotFoundException } from '@nestjs/common';
import { CommentService } from './comment.service';
import { Comment } from './comment.model';
import { CreateCommentDto, UpdateCommentDto } from './dto/create-comment.dto';
import { GraphQLAuthGuard } from '../auth/src/guards';
import { ScopeGuard, SupportAccess, RequirePermissions } from '../auth/src/guards/scope.guard';
import { ScopeContext } from '../auth/src/types/scope.types';
import { UserDataService } from '../auth/src/user-data.service';

@Resolver(() => Comment)
@UseGuards(GraphQLAuthGuard, ScopeGuard)
export class CommentResolver {
  constructor(
    private readonly commentService: CommentService,
    private readonly userDataService: UserDataService
  ) {}

  @Query(() => [Comment], { name: 'commentsByTicket' })
  @SupportAccess()
  async commentsByTicket(
    @Args('ticketId', { type: () => String }) ticketId: string,
    @Context() context: any
  ) {
    const scopeContext: ScopeContext = context.req.scopeContext;
    return this.commentService.findByTicketIdWithScope(scopeContext, ticketId);
  }

  @Query(() => Comment, { name: 'comment' })
  @SupportAccess()
  async getComment(
    @Args('id', { type: () => ID }) id: string,
    @Context() context: any
  ) {
    const scopeContext: ScopeContext = context.req.scopeContext;
    const comment = await this.commentService.findUniqueWithScope(scopeContext, { id });
    if (!comment) {
      throw new NotFoundException(`Comment with ID ${id} not found`);
    }
    return comment;
  }

  @Mutation(() => Comment)
  @RequirePermissions('canCreateComments')
  async createComment(
    @Args('data') data: CreateCommentDto,
    @Context() context: any
  ) {
    const user = context.req.user;
    const scopeContext: ScopeContext = context.req.scopeContext;

    // Extract user ID from JWT
    const userId = user.sub || user.id;

    // Extract access token from cookies for user data fetching
    const accessToken = this.userDataService.extractAccessToken(context.req.cookies);

    // Fetch user data from external auth service
    const userData = await this.userDataService.getCachedUserData(userId, accessToken);

    // Partner information is already available in scopeContext
    const partnerInfo = scopeContext.partnerInfo;

    // Prepare comment data with all available information (same as REST API)
    const commentData = {
      message: data.message,
      ticketId: data.ticketId,
      authorId: userId,
      // User data fields from /auth/users/{id} endpoint
      firstName: userData?.first_name,
      lastName: userData?.last_name,
      email: userData?.email,
      // Partner information fields as per requirements
      partnerUserId: userId, // Store user's ID (sub from JWT) in partnerUserId field
      partnerRole: scopeContext.scope, // Store user's scope in partnerRole field
      partnerOrgId: partnerInfo?.public_uid, // Store partner's public_uid in partnerOrgId field
      entSet: scopeContext.tenantType, // Tenant type (partner/account)
      // User display fields for comment model
      authorName: userData ? `${userData.first_name || ''} ${userData.last_name || ''}`.trim() : undefined,
      authorEmail: userData?.email
    };

    return this.commentService.createWithScope(scopeContext, commentData);
  }

  @Mutation(() => Comment)
  @RequirePermissions('canUpdateComments')
  async updateComment(
    @Args('id', { type: () => ID }) id: string,
    @Args('data') data: UpdateCommentDto,
    @Context() context: any
  ) {
    const scopeContext: ScopeContext = context.req.scopeContext;
    return this.commentService.updateWithScope(scopeContext, { id }, data);
  }

  @Mutation(() => Comment)
  @RequirePermissions('canDeleteComments')
  async deleteComment(
    @Args('id', { type: () => ID }) id: string,
    @Context() context: any
  ) {
    const scopeContext: ScopeContext = context.req.scopeContext;
    return this.commentService.deleteWithScope(scopeContext, { id });
  }
}
