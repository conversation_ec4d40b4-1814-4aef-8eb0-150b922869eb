import { ObjectType, Field } from '@nestjs/graphql';
import { SupportFile } from '../backblaze/support-file.model';

@ObjectType()
export class Comment {
  @Field(() => String)
  id: string;

  @Field()
  message: string;

  @Field()
  authorId: string;

  @Field()
  ticketId: string;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;

  // User data fields from auth API
  @Field({ nullable: true, description: 'First name of the comment author (from auth service)' })
  firstName?: string;

  @Field({ nullable: true, description: 'Last name of the comment author (from auth service)' })
  lastName?: string;

  @Field({ nullable: true, description: 'Email of the comment author (from auth service)' })
  email?: string;

  // Partner-related fields
  @Field({ nullable: true, description: 'Partner user ID (JWT sub) of the comment author' })
  partnerUserId?: string;

  @Field({ nullable: true, description: 'Partner role/scope of the comment author (e.g., partner:support:user)' })
  partnerRole?: string;

  @Field({ nullable: true, description: 'Partner organization ID (public_uid from partner service)' })
  partnerOrgId?: string;

  @Field({ nullable: true, description: 'Entity set type (partner/account) from JWT' })
  entSet?: string;

  // User display fields
  @Field({ nullable: true, description: 'Display name of the comment author' })
  authorName?: string;

  @Field({ nullable: true, description: 'Display email of the comment author' })
  authorEmail?: string;

  @Field(() => [SupportFile], { nullable: true })
  files?: SupportFile[];
}
