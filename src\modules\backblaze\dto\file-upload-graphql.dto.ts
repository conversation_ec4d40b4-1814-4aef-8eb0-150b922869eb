import { InputType, Field } from '@nestjs/graphql';
import { IsString, IsOptional, IsUUID } from 'class-validator';
import { GraphQLUpload, FileUpload } from 'graphql-upload-minimal';

@InputType()
export class FileUploadInput {
  @Field(() => GraphQLUpload, { description: 'File to upload' })
  file: FileUpload;

  @Field({ nullable: true, description: 'ID of the ticket to attach the file to. Either ticketId or commentId must be provided, but not both.' })
  @IsOptional()
  @IsUUID()
  ticketId?: string;

  @Field({ nullable: true, description: 'ID of the comment to attach the file to. Either ticketId or commentId must be provided, but not both.' })
  @IsOptional()
  @IsUUID()
  commentId?: string;
}

@InputType()
export class MultipleFileUploadInput {
  @Field(() => [GraphQLUpload], { description: 'Files to upload' })
  files: FileUpload[];

  @Field({ nullable: true, description: 'ID of the ticket to attach the files to. Either ticketId or commentId must be provided, but not both.' })
  @IsOptional()
  @IsUUID()
  ticketId?: string;

  @Field({ nullable: true, description: 'ID of the comment to attach the files to. Either ticketId or commentId must be provided, but not both.' })
  @IsOptional()
  @IsUUID()
  commentId?: string;
}
